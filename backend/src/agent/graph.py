import os

from agent.tools_and_schemas import SearchQueryList, Reflection
from dotenv import load_dotenv
from langchain_core.messages import AIMessage
from langgraph.types import Send
from langgraph.graph import StateGraph
from langgraph.graph import START, END
from langchain_core.runnables import RunnableConfig
# from google.genai import Client # Replaced
from langchain_openai import Chat<PERSON>pen<PERSON><PERSON>
from langchain_community.tools import DuckDuckGoSearchRun

from agent.state import (
    OverallState,
    QueryGenerationState,
    ReflectionState,
    WebSearchState,
)
from agent.configuration import Configuration
from agent.prompts import (
    get_current_date,
    query_writer_instructions,
    web_searcher_instructions,
    reflection_instructions,
    answer_instructions,
)
# from langchain_google_genai import ChatGoogleGenerative<PERSON>I # Replaced
from agent.utils import get_research_topic # Removed Gemini-specific utils

load_dotenv()

# --- Environment Variable Checks ---
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY", "")
YOUR_SITE_URL = os.getenv("YOUR_SITE_URL", "http://localhost:3000")
YOUR_SITE_NAME = os.getenv("YOUR_SITE_NAME", "LangGraph Quickstart")

# --- Helper to create LLM instance ---
def get_llm(configurable: Configuration, model_type: str) -> ChatOpenAI:
    """Helper function to initialize ChatOpenAI for OpenRouter."""
    if model_type == "query":
        model_name = configurable.query_generator_model
    elif model_type == "reflection":
        model_name = configurable.reflection_model
    elif model_type == "answer":
        model_name = configurable.answer_model
    else: # Default or for web_research summarization
        # Fallback to query_generator_model if a specific one isn't defined for other uses
        model_name = configurable.query_generator_model 

    return ChatOpenAI(
        model_name=model_name,
        temperature=0.7,
        max_retries=2
    )

# Nodes
def generate_query(state: OverallState, config: RunnableConfig) -> QueryGenerationState:
    """LangGraph node that generates a search queries based on the User's question.

    Uses Gemini 2.0 Flash to create an optimized search query for web research based on
    the User's question.

    Args:
        state: Current graph state containing the User's question
        config: Configuration for the runnable, including LLM provider settings

    Returns:
        Dictionary with state update, including search_query key containing the generated query
    """
    configurable = Configuration.from_runnable_config(config)

    # check for custom initial search query count
    if state.get("initial_search_query_count") is None:
        state["initial_search_query_count"] = configurable.number_of_initial_queries

    llm = get_llm(configurable, "query")
    structured_llm = llm.with_structured_output(SearchQueryList)

    # Format the prompt
    current_date = get_current_date()
    formatted_prompt = query_writer_instructions.format(
        current_date=current_date,
        research_topic=get_research_topic(state["messages"]),
        number_queries=state["initial_search_query_count"],
    )
    # Generate the search queries
    result = structured_llm.invoke(formatted_prompt)
    return {"query_list": result.query}


def continue_to_web_research(state: QueryGenerationState):
    """LangGraph node that sends the search queries to the web research node.

    This is used to spawn n number of web research nodes, one for each search query.
    """
    return [
        Send("web_research", {"search_query": search_query, "id": int(idx)})
        for idx, search_query in enumerate(state["query_list"])
    ]


def web_research(state: WebSearchState, config: RunnableConfig) -> OverallState:
    """LangGraph node that performs web research using DuckDuckGo and summarizes with OpenRouter."""
    configurable = Configuration.from_runnable_config(config)
    search_query = state["search_query"]
    
    print(f"--- Performing web research for query: {search_query} ---")
    search_tool = DuckDuckGoSearchRun()
    # Note: DuckDuckGoSearchRun might return a string with multiple results.
    # Depending on the length and detail, you might want to process it further
    # or adjust the summarization prompt.
    search_results_text = search_tool.run(search_query)
    print(f"--- Raw search results for '{search_query}':\n{search_results_text[:1000]}... ---") # Log first 1000 chars

    # Use an LLM to summarize the search results in the context of the original query
    summarizer_llm = get_llm(configurable, "query") # Or a dedicated model type like "summarizer"

    # The web_searcher_instructions prompt might need to be updated for this new flow
    # For now, creating a direct summarization prompt:
    summarization_prompt_template = (
        "Original research query: {query}\n\n"
        "Web search results:\n{results}\n\n"
        "Based on the web search results, please provide a concise summary that directly addresses the original research query. "
        "Extract key information and present it clearly. If the results are extensive, focus on the most relevant parts."
    )
    
    formatted_summarization_prompt = summarization_prompt_template.format(query=search_query, results=search_results_text)
    
    summary_text = summarizer_llm.invoke(formatted_summarization_prompt).content
    print(f"--- Summarized research for '{search_query}':\n{summary_text[:500]}... ---")
    
    # Simplified sources_gathered. 
    # DuckDuckGoSearchRun doesn't provide structured source URLs like the previous Gemini tool.
    # We can include the raw search text snippet or a general source attribution.
    sources_gathered = [
        {
            "type": "web_search_summary",
            "query": search_query,
            "summary_snippet": summary_text[:200], # A snippet of the summary
            "original_results_snippet": search_results_text[:500] # A snippet of raw results
        }
    ]

    return {
        "sources_gathered": sources_gathered, # This structure is different from before
        "search_query": [search_query], # Keep track of the query that was run
        "web_research_result": [summary_text], # The LLM-generated summary
    }


def reflection(state: OverallState, config: RunnableConfig) -> ReflectionState:
    """LangGraph node that identifies knowledge gaps and generates potential follow-up queries.

    Analyzes the current summary to identify areas for further research and generates
    potential follow-up queries. Uses structured output to extract
    the follow-up query in JSON format.

    Args:
        state: Current graph state containing the running summary and research topic
        config: Configuration for the runnable, including LLM provider settings

    Returns:
        Dictionary with state update, including search_query key containing the generated follow-up query
    """
    configurable = Configuration.from_runnable_config(config)
    # Increment the research loop count and get the reasoning model
    state["research_loop_count"] = state.get("research_loop_count", 0) + 1
    reasoning_model = state.get("reasoning_model") or configurable.reasoning_model

    # Format the prompt
    current_date = get_current_date()
    formatted_prompt = reflection_instructions.format(
        current_date=current_date,
        research_topic=get_research_topic(state["messages"]),
        summaries="\n\n---\n\n".join(state["web_research_result"]),
    )
    llm = get_llm(configurable, "reflection") # Use 'reflection' model type
    result = llm.with_structured_output(Reflection).invoke(formatted_prompt)

    return {
        "is_sufficient": result.is_sufficient,
        "knowledge_gap": result.knowledge_gap,
        "follow_up_queries": result.follow_up_queries,
        "research_loop_count": state["research_loop_count"],
        "number_of_ran_queries": len(state["search_query"]),
    }


def evaluate_research(
    state: ReflectionState,
    config: RunnableConfig,
) -> OverallState:
    """LangGraph routing function that determines the next step in the research flow.

    Controls the research loop by deciding whether to continue gathering information
    or to finalize the summary based on the configured maximum number of research loops.

    Args:
        state: Current graph state containing the research loop count
        config: Configuration for the runnable, including max_research_loops setting

    Returns:
        String literal indicating the next node to visit ("web_research" or "finalize_summary")
    """
    configurable = Configuration.from_runnable_config(config)
    max_research_loops = (
        state.get("max_research_loops")
        if state.get("max_research_loops") is not None
        else configurable.max_research_loops
    )
    if state["is_sufficient"] or state["research_loop_count"] >= max_research_loops:
        return "finalize_answer"
    else:
        return [
            Send(
                "web_research",
                {
                    "search_query": follow_up_query,
                    "id": state["number_of_ran_queries"] + int(idx),
                },
            )
            for idx, follow_up_query in enumerate(state["follow_up_queries"])
        ]


def finalize_answer(state: OverallState, config: RunnableConfig):
    """LangGraph node that finalizes the research summary.

    Prepares the final output by deduplicating and formatting sources, then
    combining them with the running summary to create a well-structured
    research report with proper citations.

    Args:
        state: Current graph state containing the running summary and sources gathered

    Returns:
        Dictionary with state update, including running_summary key containing the formatted final summary with sources
    """
    configurable = Configuration.from_runnable_config(config)
    reasoning_model = state.get("reasoning_model") or configurable.reasoning_model

    # Format the prompt
    current_date = get_current_date()
    formatted_prompt = answer_instructions.format(
        current_date=current_date,
        research_topic=get_research_topic(state["messages"]),
        summaries="\n---\n\n".join(state["web_research_result"]),
    )

    llm = get_llm(configurable, "answer") # Use 'answer' model type
    result = llm.invoke(formatted_prompt)

    # The structure of sources_gathered has changed.
    # The old logic for replacing short_urls is no longer applicable.
    # We can pass through the sources_gathered from the state or select relevant parts.
    # For now, let's pass through the existing 'sources_gathered' from the web_research phase.
    # This list will accumulate if multiple web_research steps occur.
    unique_sources = state.get("sources_gathered", [])

    return {
        "messages": [AIMessage(content=result.content)],
        "sources_gathered": unique_sources,
    }


# Create our Agent Graph
builder = StateGraph(OverallState, config_schema=Configuration)

# Define the nodes we will cycle between
builder.add_node("generate_query", generate_query)
builder.add_node("web_research", web_research)
builder.add_node("reflection", reflection)
builder.add_node("finalize_answer", finalize_answer)

# Set the entrypoint as `generate_query`
# This means that this node is the first one called
builder.add_edge(START, "generate_query")
# Add conditional edge to continue with search queries in a parallel branch
builder.add_conditional_edges(
    "generate_query", continue_to_web_research, ["web_research"]
)
# Reflect on the web research
builder.add_edge("web_research", "reflection")
# Evaluate the research
builder.add_conditional_edges(
    "reflection", evaluate_research, ["web_research", "finalize_answer"]
)
# Finalize the answer
builder.add_edge("finalize_answer", END)

graph = builder.compile(name="pro-search-agent")
