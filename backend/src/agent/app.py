# mypy: disable - error - code = "no-untyped-def,misc"
import os
import pathlib
from dotenv import load_dotenv

# Construct the path to the .env file relative to this script
# app.py is in backend/src/agent/, .env is in backend/
dotenv_path = pathlib.Path(__file__).resolve().parent.parent.parent / '.env'
print(f"Attempting to load .env file from: {dotenv_path}")
if dotenv_path.exists():
    load_dotenv(dotenv_path=dotenv_path)
    print(".env file loaded.")
    print(f"DATABASE_URI from env: {os.environ.get('DATABASE_URI')}")
    print(f"REDIS_URI from env: {os.environ.get('REDIS_URI')}")
    print(f"OPENROUTER_API_KEY from env: {os.environ.get('OPENROUTER_API_KEY')}")
else:
    print(".env file not found at the specified path!")

import pathlib # This import was duplicated, ensure it's only once if not already handled
from fastapi import FastAPI, Request, Response
from fastapi.staticfiles import StaticFiles
import fastapi.exceptions
from langserve import add_routes
from .graph import graph as agent_runnable # Import our compiled graph

# Define the FastAPI app
app = FastAPI()

# Add LangGraph API routes under the /api path
add_routes(
    app,
    agent_runnable,
    path="/api",
    config_keys=["configurable"], # Allows passing runtime config to the graph
)


def create_frontend_router(build_dir="../frontend/dist"):
    """Creates a router to serve the React frontend.

    Args:
        build_dir: Path to the React build directory relative to this file.

    Returns:
        A Starlette application serving the frontend.
    """
    build_path = pathlib.Path(__file__).parent.parent.parent / build_dir
    static_files_path = build_path / "assets"  # Vite uses 'assets' subdir

    if not build_path.is_dir() or not (build_path / "index.html").is_file():
        print(
            f"WARN: Frontend build directory not found or incomplete at {build_path}. Serving frontend will likely fail."
        )
        # Return a dummy router if build isn't ready
        from starlette.routing import Route

        async def dummy_frontend(request):
            return Response(
                "Frontend not built. Run 'npm run build' in the frontend directory.",
                media_type="text/plain",
                status_code=503,
            )

        return Route("/{path:path}", endpoint=dummy_frontend)

    build_dir = pathlib.Path(build_dir)

    react = FastAPI(openapi_url="")
    react.mount(
        "/assets", StaticFiles(directory=static_files_path), name="static_assets"
    )

    @react.get("/{path:path}")
    async def handle_catch_all(request: Request, path: str):
        fp = build_path / path
        if not fp.exists() or not fp.is_file():
            fp = build_path / "index.html"
        return fastapi.responses.FileResponse(fp)

    return react


# Mount the frontend under /app to not conflict with the LangGraph API routes
app.mount(
    "/app",
    create_frontend_router(),
    name="frontend",
)
