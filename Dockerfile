FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3-pip \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app
COPY backend/ .

# Install Python dependencies
RUN pip install --no-cache-dir -e .
RUN pip install --no-cache-dir sse_starlette

# Set environment variables
ENV PORT=8123
EXPOSE 8123

# Run the application
CMD ["uvicorn", "src.agent.app:app", "--host", "0.0.0.0", "--port", "8123"]
