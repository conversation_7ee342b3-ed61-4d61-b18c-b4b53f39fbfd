services:
  app:
    build: .
    ports:
      - "8123:8123"
    environment:
      OPENROUTER_API_KEY: ${OPENROUTER_API_KEY:-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}
      YOUR_SITE_URL: ${YOUR_SITE_URL:-http://localhost:3000}
      YOUR_SITE_NAME: ${YOUR_SITE_NAME:-LangGraph Quickstart}
      DATABASE_URI: sqlite:///./app.db
      REDIS_URI: redis://redis:6379
    depends_on:
      - redis

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
